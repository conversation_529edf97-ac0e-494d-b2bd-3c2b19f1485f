import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DeleteConfirm } from "@/components/ui/delete-confirm";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import {
  Calendar,
  ChevronDown,
  Copy,
  Edit,
  Eye,
  FileText,
  Loader2,
  Plus,
  Search,
  Trash2,
  ListOrdered,
  CheckCircle,
  Circle,
  MessageSquare,
  Phone,
  UserPlus,
  BarChart3,
} from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { formApi } from "../../services/api/form";
import { FormConfig, TemplateResponse, Page } from "./types/form-types";
import { FormSubmissionsModal } from "./components/FormSubmissionsModal";
import { FormPagination } from "./components/FormPagination";

interface FormListProps {
  projectId: string;
  toDesigner: () => void;
}

// 模板图标映射
const getTemplateIcon = (templateName: string) => {
  const name = templateName.toLowerCase();
  if (name.includes('feedback') || name.includes('反馈')) {
    return MessageSquare;
  } else if (name.includes('contact') || name.includes('联系')) {
    return Phone;
  } else if (name.includes('registration') || name.includes('注册')) {
    return UserPlus;
  } else if (name.includes('survey') || name.includes('调查')) {
    return BarChart3;
  }
  return FileText;
};

// 模板颜色映射
const getTemplateColor = (templateName: string) => {
  const name = templateName.toLowerCase();
  if (name.includes('feedback') || name.includes('反馈')) {
    return 'bg-green-100 text-green-600';
  } else if (name.includes('contact') || name.includes('联系')) {
    return 'bg-purple-100 text-purple-600';
  } else if (name.includes('registration') || name.includes('注册')) {
    return 'bg-orange-100 text-orange-600';
  } else if (name.includes('survey') || name.includes('调查')) {
    return 'bg-red-100 text-red-600';
  }
  return 'bg-blue-100 text-blue-600';
};

// 模板卡片组件
const TemplateCard = ({
  template,
  onPreviewTemplate,
  onUseTemplate
}: {
  template: TemplateResponse;
  onPreviewTemplate: (template: TemplateResponse) => void;
  onUseTemplate: (template: TemplateResponse) => void;
}) => {
  const Icon = getTemplateIcon(template.name);
  const colorClass = getTemplateColor(template.name);

  const handleCardClick = () => {
    onPreviewTemplate(template);
  };

  const handleUseTemplate = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡
    onUseTemplate(template);
  };

  return (
    <Card
      className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105"
      onClick={handleCardClick}
    >
      <CardContent className="p-6">
        <div className={`w-12 h-12 ${colorClass} rounded-lg flex items-center justify-center mb-4`}>
          <Icon className="w-6 h-6" />
        </div>
        <h3 className="font-semibold text-gray-900 mb-2">{template.name}</h3>
        <p className="text-sm text-gray-600 mb-4 line-clamp-2">
          {template.description || 'No description available'}
        </p>
        <Button
          variant="ghost"
          size="sm"
          className="text-blue-600 hover:text-blue-700 p-0"
          onClick={handleUseTemplate}
        >
          Use Template →
        </Button>
      </CardContent>
    </Card>
  );
};

export const FormList = ({ projectId, toDesigner }: FormListProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { t } = useTranslation();

  const [forms, setForms] = useState<FormConfig[]>([]);
  const [templates, setTemplates] = useState<TemplateResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [selectedFormId, setSelectedFormId] = useState<string | null>(null);
  const [showSubmissionsModal, setShowSubmissionsModal] = useState(false);

  // 模板分页状态
  const [templatePage, setTemplatePage] = useState(1);
  const [templateTotal, setTemplateTotal] = useState(0);
  const getTemplatePageSize = (page: number) => page === 1 ? 3 : 4; // 第一页3个，后续页面4个

  // 表单分页状态
  const [formPage, setFormPage] = useState(1);
  const [formTotal, setFormTotal] = useState(0);
  const formPageSize = 6; // 两行最多6个表单 (3x2)

  useEffect(() => {
    if (projectId) {
      loadForms();
      loadTemplates();
    }
  }, [projectId]);

  useEffect(() => {
    loadTemplates();
  }, [templatePage]);

  useEffect(() => {
    loadForms();
  }, [formPage]);

  const loadTemplates = async () => {
    try {
      const pageSize = getTemplatePageSize(templatePage);
      const result = await formApi.getFormTemplates(templatePage, pageSize);

      if (Array.isArray(result)) {
        // 非分页响应
        setTemplates(result);
        setTemplateTotal(result.length);
      } else {
        // 分页响应
        setTemplates(result.items);
        setTemplateTotal(result.total);
      }
    } catch (err) {
      console.error(err);
    }
  };

  const loadForms = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await formApi.getProjectForms(projectId, formPage, formPageSize);

      if (Array.isArray(result)) {
        // 非分页响应
        setForms(result);
        setFormTotal(result.length);
      } else {
        // 分页响应
        setForms(result.items);
        setFormTotal(result.total);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "加载表单列表失败");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateForm = () => {
    toDesigner();
  };

  const handlePreviewTemplate = (template: TemplateResponse) => {
    navigate(`/form-preview/template-${template.id}`);
  };

  const handleCreateFromTemplate = async (template: TemplateResponse) => {
    try {
      let content;
      try {
        content = JSON.parse(template.content);
      } catch (e) {
        console.error("Failed to parse template content", e);
        return;
      }
      const templateConfig = {
        formId: "",
        title: template.name,
        description: template.description,
        logo: template.logo,
        ...content,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      await formApi.saveForm(projectId, templateConfig);
      toast({
        title: t("templateCreatedSuccessfully"),
        description: `${templateConfig.title}${t("templateCreated")}`,
      });
      loadForms(); // 重新加载列表
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("templateCreationFailed"),
        description:
          error instanceof Error ? error.message : t("templateCreationFailed"),
      });
    }
  };

  const handleEditForm = (formId: string) => {
    navigate(`/form-builder?formId=${formId}`);
  };

  const handlePreviewForm = (formId: string) => {
    navigate(`/form-preview/${formId}`);
  };

  const handleDuplicateForm = async (form: FormConfig) => {
    try {
      await formApi.duplicateForm(form.formId);
      toast({
        title: t("copySuccess"),
        description: t("formCopiedSuccessfully"),
      });
      loadForms(); // 重新加载列表
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("copyFailed"),
        description:
          error instanceof Error ? error.message : t("formCopyFailed"),
      });
    }
  };

  const handleSetFormActive = async (formId: string, currentActiveState: boolean) => {
    // 如果表单已经是激活状态，则不执行任何操作
    if (currentActiveState) return;

    try {
      await formApi.setActive(formId);
      toast({
        title: t("active"),
        description: t("saveConfig"),
      });
      loadForms(); // 重新加载列表以显示更新后的状态
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("saveFailed"),
        description:
          error instanceof Error ? error.message : t("saveFailed"),
      });
    }
  };

  const handleDeleteForm = async (formId: string) => {
    try {
      await formApi.deleteForm(formId);
      toast({
        title: t("deleteSuccess"),
        description: t("formDeletedSuccessfully"),
      });
      loadForms(); // 重新加载列表
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("deleteFailed"),
        description:
          error instanceof Error ? error.message : t("formDeleteFailed"),
      });
    }
  };

  const handleCopyShareUrl = (formId: string) => {
    const shareUrl = `${window.location.origin}/form/${formId}`;
    navigator.clipboard.writeText(shareUrl);
    toast({
      title: t("linkCopied"),
      description: t("shareLinkCopied"),
    });
  };

  const handleTemplatePageChange = (page: number) => {
    setTemplatePage(page);
  };

  const handleFormPageChange = (page: number) => {
    setFormPage(page);
  };

  // 过滤表单
  const filteredForms = forms.filter(
    (form) =>
      form.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      form.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // 搜索时重置分页
  useEffect(() => {
    if (searchQuery) {
      setFormPage(1);
    }
  }, [searchQuery]);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 头部区域 */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {t("formManagement")}
            </h1>
            <p className="text-gray-600 mt-2">{t("createAndManageForms")}</p>
          </div>

          <div className="flex items-center space-x-3">
            {/* 搜索框 */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder={t("searchForms")}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
          </div>
        </div>

        {/* 模板区域 */}
        <div className="mb-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* From Scratch 卡片 - 只在第一页显示 */}
            {templatePage === 1 && (
              <Card
                className="cursor-pointer hover:shadow-lg transition-all duration-200 border-2 border-dashed border-gray-300 hover:border-blue-400"
                onClick={handleCreateForm}
              >
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Plus className="w-6 h-6 text-gray-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">From Scratch</h3>
                  <p className="text-sm text-gray-600 mb-4">Start with a blank canvas</p>
                </CardContent>
              </Card>
            )}

            {/* 动态渲染模板卡片 */}
            {templates.map((template) => (
              <TemplateCard
                key={template.id}
                template={template}
                onPreviewTemplate={handlePreviewTemplate}
                onUseTemplate={handleCreateFromTemplate}
              />
            ))}
          </div>

          {/* 模板分页 */}
          <FormPagination
            currentPage={templatePage}
            totalItems={templateTotal}
            pageSize={getTemplatePageSize(templatePage)}
            onPageChange={handleTemplatePageChange}
            className="mt-6"
          />
        </div>

        {/* My Forms 区域 */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">My Forms</h2>
            {filteredForms.length > 0 && (
              <Button variant="ghost" className="text-blue-600 hover:text-blue-700">
                View All
              </Button>
            )}
          </div>

          {filteredForms.length === 0 ? (
            <Card className="border-dashed border-2">
              <CardContent className="py-16 text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FileText className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchQuery ? t("noFormsFound") : "No forms yet"}
                </h3>
                <p className="text-gray-600 mb-6">
                  {searchQuery ? t("tryDifferentSearch") : "Create your first form to get started"}
                </p>
                {!searchQuery && (
                  <Button onClick={handleCreateForm} className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="w-4 h-4 mr-2" />
                    {t("newForm")}
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {filteredForms.map((form) => (
                <Card
                  key={form.formId}
                  className="hover:shadow-lg transition-all duration-200 hover:scale-[1.02]"
                >
                <CardContent className="p-6">
                  {/* 表单状态指示器 */}
                  <div className="flex items-center justify-between mb-4">
                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      form.active
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {form.active ? (
                        <>
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Active
                        </>
                      ) : (
                        <>
                          <Circle className="w-3 h-3 mr-1" />
                          Inactive
                        </>
                      )}
                    </div>
                  </div>

                  {/* 表单标题和描述 */}
                  <div className="mb-4">
                    <h3 className="font-semibold text-gray-900 text-lg mb-2 line-clamp-1">
                      {form.title}
                    </h3>
                    <p className="text-sm text-gray-600 line-clamp-2 min-h-[2.5rem]">
                      {form.description || 'No description provided'}
                    </p>
                  </div>

                  {/* 表单统计 */}
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center space-x-1">
                      <FileText className="w-4 h-4" />
                      <span>
                        {form.components.length} {t("components")}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>
                        {new Date(form.updatedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditForm(form.formId)}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{t("editForm")}</p>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handlePreviewForm(form.formId)}
                            className="h-8 w-8 p-0"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{t("previewForm")}</p>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSelectedFormId(form.formId);
                              setShowSubmissionsModal(true);
                            }}
                            className="h-8 w-8 p-0"
                          >
                            <ListOrdered className="w-4 h-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{t("viewSubmissions")}</p>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleSetFormActive(form.formId, !!form.active)}
                            className={`h-8 w-8 p-0 ${form.active ? "text-green-600" : "text-gray-600"}`}
                            disabled={!!form.active}
                          >
                            {form.active ? (
                              <CheckCircle className="w-4 h-4" />
                            ) : (
                              <Circle className="w-4 h-4" />
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{form.active ? t("deactivateForm") : t("activateForm")}</p>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDuplicateForm(form)}
                            className="h-8 w-8 p-0"
                          >
                            <Copy className="w-4 h-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{t("copyForm")}</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <DeleteConfirm
                          title={t("delete") + t("form")}
                          description={`${t("confirmDeleteForm")}"${
                            form.title
                          }"${t("deleteFormWarning")}`}
                          onConfirm={() => handleDeleteForm(form.formId)}
                        >
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </DeleteConfirm>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{t("deleteForm")}</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          )}

          {/* 表单分页 */}
          {filteredForms.length > 0 && (
            <FormPagination
              currentPage={formPage}
              totalItems={formTotal}
              pageSize={formPageSize}
              onPageChange={handleFormPageChange}
              className="mt-6"
            />
          )}
        </div>
      </div>

      <FormSubmissionsModal
        formId={selectedFormId || ""}
        open={showSubmissionsModal}
        onOpenChange={setShowSubmissionsModal}
      />
    </div>
  );
};
