import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { ArrowLeft, Eye, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { formApi } from "../../services/api/form";
import { TemplateResponse, FormConfig } from "./types/form-types";
import { ComponentRenderer } from "./components/ComponentRenderer";
import { useProject } from "@/context/ProjectContext";

const TemplatePreview = () => {
  const { templateId } = useParams<{ templateId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { t } = useTranslation();
  const { currentProject } = useProject();
  
  const [template, setTemplate] = useState<TemplateResponse | null>(null);
  const [templateConfig, setTemplateConfig] = useState<FormConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);

  useEffect(() => {
    if (templateId) {
      loadTemplate();
    }
  }, [templateId]);

  const loadTemplate = async () => {
    try {
      setLoading(true);
      const templates = await formApi.getFormTemplates();
      const foundTemplate = templates.find(t => t.id === templateId);
      
      if (!foundTemplate) {
        toast({
          variant: "destructive",
          title: "Template not found",
          description: "The requested template could not be found.",
        });
        navigate("/form-builder");
        return;
      }

      setTemplate(foundTemplate);
      
      // 解析模板内容
      try {
        const content = JSON.parse(foundTemplate.content);
        const config: FormConfig = {
          formId: "",
          title: foundTemplate.name,
          description: foundTemplate.description || "",
          logo: foundTemplate.logo,
          url: "",
          settings: content.settings || {
            submitButtonText: "Submit",
            successMessage: "Thank you for your submission!",
            allowMultipleSubmissions: false,
            requireAuth: false,
            collectEmail: false,
            theme: {
              primaryColor: "#3B82F6",
              backgroundColor: "#FFFFFF",
              textColor: "#1F2937",
            },
          },
          components: content.components || [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          active: false,
        };
        setTemplateConfig(config);
      } catch (e) {
        console.error("Failed to parse template content", e);
        toast({
          variant: "destructive",
          title: "Invalid template",
          description: "The template content is corrupted.",
        });
        navigate("/form-builder");
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Failed to load template",
        description: error instanceof Error ? error.message : "Unknown error",
      });
      navigate("/form-builder");
    } finally {
      setLoading(false);
    }
  };

  const handleUseTemplate = async () => {
    if (!template || !templateConfig || !currentProject?.id) return;

    try {
      setCreating(true);
      await formApi.saveForm(currentProject.id, templateConfig);
      toast({
        title: t("templateCreatedSuccessfully"),
        description: `${templateConfig.title} ${t("templateCreated")}`,
      });
      navigate("/form-builder");
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("templateCreationFailed"),
        description: error instanceof Error ? error.message : t("templateCreationFailed"),
      });
    } finally {
      setCreating(false);
    }
  };

  const handleBackToList = () => {
    navigate("/form-builder");
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading template...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!template || !templateConfig) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-gray-600">Template not found</p>
            <Button onClick={handleBackToList} className="mt-4">
              Back to Forms
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={handleBackToList}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Forms</span>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{template.name}</h1>
              <p className="text-gray-600 mt-1">Template Preview</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={() => navigate(`/form-preview/template-${templateId}`)}
            >
              <Eye className="w-4 h-4 mr-2" />
              Full Preview
            </Button>
            <Button
              onClick={handleUseTemplate}
              disabled={creating}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {creating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4 mr-2" />
                  Use Template
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Template Info */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Template Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Description</h3>
                <p className="text-gray-600">
                  {template.description || "No description available"}
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Components</h3>
                <p className="text-gray-600">
                  {templateConfig.components.length} form components
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Preview */}
        <Card>
          <CardHeader>
            <CardTitle>Form Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="max-w-2xl mx-auto">
              <div className="space-y-6">
                {templateConfig.components.map((component) => (
                  <div key={component.id}>
                    <ComponentRenderer
                      component={component}
                      value=""
                      onChange={() => {}}
                      preview={true}
                    />
                  </div>
                ))}
              </div>
              
              {templateConfig.components.length > 0 && (
                <div className="mt-8 pt-6 border-t">
                  <Button className="w-full" disabled>
                    {templateConfig.settings.submitButtonText || "Submit"}
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
      </div>
    </DashboardLayout>
  );
};

export default TemplatePreview;
