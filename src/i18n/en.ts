export default {
  // Navigation
  dashboard: "Dashboard",
  projects: "Projects",
  chats: "Conversation",
  settings: "Settings",
  channels: "Channels",
  chatbot: "Chat bot",
  whatsapp: "WhatsAPP",
  voice: "Voice",
  mail: "Mail",
  workflow: "Workflow",
  form: "Form",
  pipeline: "Pipeline",
  general: "General",
  testAndInstall: "Test & Install",
  chinese: "中文",
  english: "EN",

  // Auth
  login: "Login",
  logout: "Logout",
  toDashboard: "Go to Dashboard",
  logoutDescription: "You have successfully logged out.",
  loginSuccessful: "Login successful",
  loginFailed: "Login failed",
  loginFailedDescription: "Incorrect username or password, or network error",
  register: "Register",
  email: "Email",
  password: "Password",
  confirmPassword: "Confirm Password",
  loginTitle: "Welcome Back",
  loginSubtitle: "Login to your AI Customer Service Platform",
  registerTitle: "Create Account",
  registerSubtitle: "Start building your AI customer service",
  loginButton: "Sign In",
  registerButton: "Create Account",
  continueWithGoogle: "Continue with Google",
  orContinueWith: "Or continue with",
  account: "Account",
  passwordTooWeak: "Password does not meet security requirements",
  passwordMismatch: "Passwords do not match",
  deleteAccount: "Delete Account",
  deleteAccountConfirm: "Please type your email address to confirm deletion",
  deleteAccountWarning:
    "This action cannot be undone. This will permanently delete your account and remove all your data from our servers.",
  accountDeleted: "Account deleted successfully",
  deleteAccountFailed: "Failed to delete account",
  enterPassword: "Enter your password",
  passwordRequired: "Password is required",
  deleting: "Deleting...",

  // Form Builder
  selectFile: "Select File",
  dragAndDropSupport: "Drag and drop files here or click to select",
  lastName: "Last Name",
  firstName: "First Name",
  phoneNumber: "Phone Number",
  emailAddressLabel: "Email Address",
  enterPhone: "Enter phone number",
  enterEmail: "Enter email address",
  bankCardNumber: "Bank Card Number",
  bankName: "Bank Name",
  enterBankCardNumber: "Enter bank card number",
  enterBankName: "Enter bank name",
  streetAddress: "Street Address",
  enterStreetAddress: "Enter street address",
  city: "City",
  enterCity: "Enter city",
  stateProvince: "State/Province",
  enterStateProvince: "Enter state or province",
  country: "Country",
  enterCountry: "Enter country",
  postalCode: "Postal Code",
  enterPostalCode: "Enter postal code",

  // Dashboard
  welcomeBack: "Welcome back",
  totalVisitors: "Total Visitors",
  activeProjects: "Active Project",
  totalChats: "Total Chats",
  satisfactionRate: "Satisfaction Rate",
  recentActivity: "Recent Activity",

  // Project
  createProjectSuccess: "Project created successfully",
  createProjectFailed: "Failed to create project",
  myProjects: "My Project",
  createProject: "New Project",
  projectName: "Project Name",
  websiteUrl: "Website URL",
  description: "Description",
  status: "Status",
  active: "Active",
  inactive: "Inactive",
  project: "Project",
  createSuccess: "Created successfully",
  createFailed:
    "An error occurred while creating the project. Please try again.",
  saveConfig: "Project configuration has been saved.",
  saveFailed: "Save failed. Please try again.",
  projectNotFound: "Project not found",
  returnToProjectList: "Return to project list",

  // Knowledge Base
  knowledgeBase: "Knowledge Base",
  addDocument: "Add Document",
  uploadFile: "Upload File",
  addUrl: "Add URL",
  addFaq: "Add FAQ",
  addWebsiteUrl: "Add Website URL",
  uploadDocument: "Upload Document",
  totalKnowledge: "Total Knowledge",
  totalWebsites: "Total Websites",
  totalDocuments: "Total Documents",
  externalLinks: "External Links",
  noMatchingContent: "No matching content found",
  knowledgeBaseEmpty: "Knowledge base is empty",
  tryDifferentKeywords: "Try using different keywords to search",
  addContentToStart:
    "Add website URLs or upload documents to start building your knowledge base",

  // Widget Settings
  widgetSettings: "Widget Settings",
  assistantName: "Assistant Name",
  welcomeMessage: "Welcome Message",
  widgetColor: "Widget Color",
  position: "Position",
  generateCode: "Generate Code",

  // Pricing
  pricing: "Pricing",
  starterPlan: "Starter",
  growthPlan: "Growth",
  proPlan: "Pro",
  starterPrice: "$29/month",
  growthPrice: "$99/month",
  proPrice: "$299/month",
  starterFeatures:
    "Up to 1,000 conversations/month, Basic analytics, Email support",
  growthFeatures:
    "Up to 10,000 conversations/month, Advanced analytics, Priority support, Custom branding",
  proFeatures:
    "Unlimited conversations, Enterprise analytics, 24/7 phone support, API access, Custom integrations",
  choosePlan: "Choose Plan",
  billing: "Billing & Plan",
  // Project Creation Steps
  stepWebsite: "Website Information",
  stepKnowledge: "Knowledge Base",
  stepWidget: "Widget Settings",
  stepCode: "Embed Code",
  websiteUrlPlaceholder: "https://your-website.com",
  addFaqItem: "Add FAQ",
  question: "Question",
  answer: "Answer",
  widgetPreview: "Widget Preview",
  embedCode: "Embed Code",
  copyCode: "Copy Code",

  // Common
  save: "Save",
  cancel: "Cancel",
  edit: "Edit",
  delete: "Delete",
  view: "View",
  create: "Create",
  loading: "Loading...",
  success: "Success",
  error: "Error",
  next: "Next",
  previous: "Previous",
  finish: "Finish",
  back: "Back",

  // Brand
  appName: "AI Customer Service",
  aiAssistant: "AI Assistant",
  newProject: "New Project",

  // Footer
  companyDescription:
    "Empower your business with intelligent AI customer service solutions. Provide exceptional support experience for your customers 24/7.",
  supportEmail: "<EMAIL>",
  products: "Products",
  support: "Support",
  documentation: "Documentation",
  helpCenter: "Help Center",
  contactUs: "Contact Us",
  systemStatus: "Status",
  allRightsReserved: "All rights reserved.",
  privacyPolicy: "Privacy Policy",
  termsOfService: "Terms of Service",

  // Test & Install
  testInstall: "Test & Install",
  installChatbot: "Install a chatbot on your website.",
  testChatbot: "Test your chatbot",
  testChatbotDescription: "Ensure it works perfectly",
  embedCodeDescription:
    "When you're satisfied, copy the code below and paste it before the </body> tag on your website to activate it.",
  webChatEnabled: "Web Chat is enabled",

  // Knowledge Base
  addWebsite: "Add Website",
  addWebsiteDescription:
    "Enter the website URL that you want to add to the knowledge base.",
  selectFiles: "Select Files",
  upload: "Upload",
  uploading: "Uploading...",
  documentUploaded: "Document uploaded successfully",
  uploadFailed: "Upload failed, please try again",
  manageKnowledgeBase: "Manage your AI assistant's knowledge base content",
  addKnowledgeSuccess: "Knowledge base added successfully.",
  addKnowledgeFailed: "Failed to add knowledge base, please try again",
  supportedFileTypes: "Supports PDF, DOC, DOCX, TXT, MD formats.",

  // Chat Records
  chatRecords: "Chat Records",
  totalConversations: "Total Conversations",
  totalCountry: "Total Country",
  totalCity: "Total City",
  allConversations: "All conversations",
  viewChatRecords: "View and manage all customer service conversation records",
  noConversation: "No conversation records available.",
  selectConversation: "Please select a conversation.",
  fetchConversationFailed: "Failed to retrieve conversation list.",
  fetchChatsFailed: "Failed to retrieve chat history.",
  summary: "Summary",
  detail: "Detail",
  summaryContent: "Summary content will be displayed here",

  // Analysis
  dataAnalysis: "Data Analysis",
  performanceAnalysis:
    "Customer service system performance analysis and insights",
  conversationTrend: "Conversation Trend",
  satisfactionAnalysis: "Satisfaction Analysis",
  activeHours: "Active Hours Analysis",
  exportReport: "Export Report",

  // Dashboard specific
  dashboardOverview: "Here's an overview of how your chatbot is performing",
  emailPlaceholder: "<EMAIL>",
  passwordPlaceholder: "••••••••",

  // Default project values
  defaultWelcomeMessage:
    "Welcome! 👋\nI'm Hali, here to assist with any questions you have. How can I help you today?",
  defaultSuggestedQuestion1: "No questions",
  defaultSuggestedQuestion2: "How are you?",
  defaultSuggestedQuestion3: "I have another question",

  // Hero section
  smartConversations: "Smart Conversations",
  smartConversationsDesc: "AI-powered chat with natural language processing",
  quickSetup: "Quick Setup",
  quickSetupDesc: "Deploy in minutes with simple embed code",
  secureReliable: "Secure & Reliable",
  secureReliableDesc: "Enterprise-grade security and 99.9% uptime",
  analyticsDashboard: "Analytics Dashboard",
  analyticsDashboardDesc: "Comprehensive insights and performance metrics",

  // Chat Core messages
  unknown: "Unknown",
  sessionApiNotConfigured: "Session API URL is not configured",
  sessionCreationFailed: "Failed to create session",
  chatErrorMessage: "Sorry, an error occurred. Please try again later.",
  demoResponseMessage:
    "Thank you for your question! This is a demo response. To enable real conversations, please ensure the project ID is properly configured.",

  // Account page - new keys only
  accountPreferences: "Your account preferences",
  personal: "Personal",
  security: "Security",
  personalInformation: "Personal Information",
  updatePersonalDetails: "Update your personal details and profile picture",
  name: "Name",
  enterYourName: "Enter your name",
  emailAddress: "Email address",
  enterYourEmail: "Enter your email",
  editButton: "Edit...",
  saving: "Saving...",
  securitySettings: "Security Settings",
  managePasswordSecurity: "Manage your password and account security",
  currentPassword: "Current Password",
  enterCurrentPassword: "Enter current password",
  newPassword: "New Password",
  enterNewPassword: "Enter new password",
  confirmNewPassword: "Confirm New Password",
  confirmNewPasswordPlaceholder: "Confirm new password",
  updating: "Updating...",
  updatePassword: "Update Password",
  dangerZone: "Danger Zone",
  permanentlyDeleteAccount:
    "Permanently delete your account and all associated data",
  deleteAccountButton: "Delete account...",

  // Login page - new keys only
  newToAiCustomerService: "New to AI CustomerService?",

  // Register page - new keys only
  alreadyHaveAccount: "Already have an account?",

  // Project page - new keys only
  appearance: "Appearance",
  adjustAppearance:
    "Adjust the appearance of your chat widget to match your website's style.",
  logo: "Logo",
  clickToUpload: "Upload",
  svgPngJpg: "SVG, PNG or JPG (max. 800x400px)",
  brandColor: "Brand color",
  suggestedQuestions: "Suggested questions",
  helpVisitorsStart:
    "Help visitors start a conversation by providing quick, one-click questions.",
  enterSuggestedQuestion: "Enter a suggested question...",
  addQuestion: "Add Question",
  projectIdMissing: "Project ID missing, unable to save configuration",

  // Chat Records page - new keys only
  search: "Search",
  loadingConversations: "Loading conversations...",
  conversationDetails: "Conversation details",
  id: "ID",
  channel: "Channel",
  customerInfo: "Customer info",
  browser: "Browser",
  system: "System",
  createdAt: "Created at",
  sources: "SOURCES:",
  referenceData: "Reference data",

  // Billing Plan page - new keys only
  billingPlan: "Billing & Plan",
  managePlanBilling:
    "Manage plan and billing settings for your Quickset account",
  plans: "Plans",
  yearly: "Yearly",
  monthly: "Monthly",
  mostPopular: "Most Popular",
  supportResponses: "support responses/mo",
  aiChatbots: "AI chatbots",
  sourcesForTraining: "sources for training per chatbot",
  seatsPerChatbot: "seats per chatbot",
  currentPlan: "Current Plan",
  upgrade: "Upgrade",
  loadingPlans: "Loading plans...",
  failedToFetchPlan: "Failed to fetch plan information",
  unlockProFeatures: "Unlock Pro Features",
  renewalDate: "Renewal date",
  notSet: "Not set",
  planLimitsUsage: "Plan limits usage",
  monthlyResponseCredits: "Monthly response credits",
  additionalCredits: "Additional credits",
  purchaseMore: "Purchase more",
  botLimitsUsage: "Bot limits usage",
  sourcesForTrainingLimit: "Sources for training",
  unableToLoadPlan: "Unable to load plan information",

  // Current Product Info - new keys only
  failedToLoadProductInfo: "Failed to load product information",
  unknownError: "Unknown error",
  expired: "Expired",
  expiresInDays: "Expires in {days} days",
  productExpired: "{productName} has expired",
  currentProduct: "You're enjoying {productName} access",
  productExpiredMessage:
    "Your subscription has expired. Please renew to continue using all features.",
  productExpiringSoonMessage:
    "Your subscription expires {expireTime}. Renew now to avoid service interruption.",
  productActiveUntil: "Your subscription is active until {expireTime}.",
  renewNow: "Renew now",
  manageSubscription: "Manage subscription",
  expiresOn: "Expires on {date}",
  expiringSoon: "Expiring soon",

  // Documentation page - new keys only
  searchDocumentation: "Search documentation...",
  noMatchingDocuments: "No matching documents found",
  documents: "documents",
  tableOfContents: "Table of Contents",
  selectDocument: "Select a Document",
  chooseDocumentFromSidebar: "Choose a document from the left sidebar to view",

  // NotFound page - new keys only
  pageNotFound: "Oops! Page not found",
  returnToHome: "Return to Home",

  // OAuth Callback page - new keys only
  signingYouIn: "Signing you in...",
  pleaseWaitAuthentication: "Please wait while we complete your authentication",
  authenticationFailed: "Authentication Failed",
  backToLogin: "Back to Login",

  // Onboarding pages - new keys only
  websiteAddress: "What's your website address?",
  provideWebsiteUrl:
    "Provide your website URL to help train your AI agent. We'll start by pulling in key data to get your chatbot ready in minutes.",
  enterWebsiteAddress: "Enter your website address",
  completeLater: "You can complete this step later",
  customizeAiAgent: "Customize your AI Agent",
  matchWebsiteColors:
    "We tried to match your website with your colors, logos, and brand personality. Customize it below:",
  brandName: "Brand name",
  aiAgentReady: "Your AI Agent is ready!",
  satisfiedCopyCode:
    "When you're satisfied, copy the code below and paste it before the closing </body> tag on your website to activate it.",
  copied: "Copied!",
  copy: "Copy",
  testAiAgent: "Go ahead, test your AI agent now. Try the examples below:",
  testWithExamples: "Test with these examples:",
  creating: "Creating...",
  startNow: "Start Now",

  // Test Install page - new keys only
  seeHowToInstall: "See how to install the Web Chat widget with:",

  // KnowledgeBase page - new keys only
  title: "Title",
  lastTrained: "Last trained",
  actions: "Actions",
  filter: "Filter",
  failedToLoadKnowledge: "Failed to load knowledge base",
  selectedFiles: "Selected files:",
  openLink: "Open link",
  deleteItem: "Delete item",

  // Pagination
  showing: "Showing",
  to: "to",
  of: "of",
  items: "items",
  previousPage: "Previous",
  nextPage: "Next",

  // Additional missing keys
  pageNotFoundDescription:
    "The page you're looking for doesn't exist or has been moved.",

  // Form Builder
  formBuilder: "Form Builder",
  formList: "Form List",
  formDesigner: "Form Designer",
  newForm: "New Form",
  formTitle: "Form Title",
  formDescription: "Form Description",
  formLogo: "Form Logo",
  formTitlePlaceholder: "Please enter form title",
  formDescriptionPlaceholder: "Please enter form description",
  uploadLogo: "Upload Logo",
  changeLogo: "Change Logo",
  removeLogo: "Remove Logo",
  logoUploadTip: "Support JPG, PNG format, recommended size 200x200px",
  logoUploadError: "Logo upload failed",
  logoUploadSuccess: "Logo uploaded successfully",
  logoFormatError: "Please select JPG or PNG format image",
  logoSizeError: "Image size cannot exceed 2MB",
  uploadSuccess: "Upload successful",
  saveForm: "Save Form",
  savingForm: "Saving...",
  previewForm: "Preview Form",
  undo: "Undo",
  redo: "Redo",
  loadSuccess: "Load Success",
  loadFailed: "Load Failed",
  saveSuccess: "Save Success",
  deleteSuccess: "Delete Success",
  deleteFailed: "Delete Failed",
  copySuccess: "Copy Success",
  copyFailed: "Copy Failed",
  formLoadedSuccessfully: "Form loaded successfully",
  formLoadFailed: "Failed to load form",
  formSavedSuccessfully: "Form saved successfully",
  formSaveFailed: "Failed to save form",
  formSubmissions: "Form Submissions",
  totalSubmissions: "Total Submissions",
  noSubmissionsYet: "No submissions yet",
  noSubmissionsDescription: "When users submit this form, their responses will appear here.",
  loadSubmissionsFailed: "Failed to load submissions",
  showingPage: "Showing page {{currentPage}} of {{totalPages}} ({{totalItems}} items total)",
  formDeletedSuccessfully: "Form deleted successfully",
  formDeleteFailed: "Failed to delete form",
  formCopiedSuccessfully: "Form copied successfully",
  formCopyFailed: "Failed to copy form",
  previewFailed: "Preview failed",
  pleaseFirstSaveForm: "Please save the form first",
  formManagement: "Form Management",
  createAndManageForms: "Create and manage your forms",
  searchForms: "Search forms...",
  noFormsFound: "No matching forms found",
  noFormsYet: "No forms yet",
  tryDifferentSearch: "Try using different search terms",
  createFirstForm: "Create your first form to start collecting data",
  components: "components",
  template: "Template",
  contactForm: "Contact Form",
  registrationForm: "Registration Form",
  feedbackForm: "Feedback Form",
  templateCreatedSuccessfully: "Template created successfully",
  templateCreationFailed: "Creation failed",
  templateCreated: "created",
  shareLink: "Share Link",
  linkCopied: "Link copied",
  shareLinkCopied: "Share link copied to clipboard",
  confirmDeleteForm: "Are you sure you want to delete form",
  deleteFormWarning: "? This action cannot be undone.",
  formNotExist: "Form does not exist",
  loadFormFailed: "Failed to load form",
  getFormListFailed: "Failed to get form list",
  activateForm: "Activate form",
  deactivateForm: "Deactivate form",

  // Form Designer Components
  componentLibrary: "Component Library",
  searchComponents: "Search components...",
  basicComponents: "Basic Components",
  layoutComponents: "Layout Components",
  templateComponents: "Template Components",
  basic: "Basic",
  layout: "Layout",
  common: "Common",

  // Component Names
  textInput: "Text Input",
  multilineText: "Multiline Text",
  dropdown: "Dropdown",
  radioButton: "Radio Button",
  checkbox: "Checkbox",
  datePicker: "Date Picker",
  numberInput: "Number Input",
  emailInput: "Email Input",
  phoneInput: "Phone Input",
  heading: "Heading",
  paragraph: "Paragraph",
  divider: "Divider",
  fullName: "Full Name",
  contactInfo: "Contact Info",
  address: "Address",
  idNumber: "ID Number",
  bankCard: "Bank Card",

  // Component Descriptions
  singleLineTextInput: "Single line text input",
  multilineTextInput: "Multiline text input",
  dropdownSelect: "Dropdown select",
  radioButtonGroup: "Radio button group",
  checkboxGroup: "Checkbox group",
  datePickerComponent: "Date picker component",
  numberInputBox: "Number input box",
  emailInputBox: "Email input box",
  phoneInputBox: "Phone number input box",
  headingText: "Heading text",
  paragraphText: "Paragraph text",
  dividerLine: "Divider line",
  fullNameInput: "Full name input",
  phoneAndEmailInput: "Phone and email input",
  fullAddressInput: "Address input",
  idNumberInput: "ID number input",
  bankCardInput: "Bank card information input",

  // Placeholders
  enterText: "Enter text",
  enterMultilineText: "Enter multiline text",
  pleaseSelect: "Please select",
  selectDate: "Select date",
  enterNumber: "Enter number",
  enterIdNumber: "Enter ID number",

  // Default Values
  option: "Option",
  idCardNumber: "ID Card Number",
  bankCardInfo: "Bank Card Info",
  titleText: "Title Text",
  paragraphContent: "This is a paragraph content",


  // Property Panel
  selectComponent: "Select Component",
  clickComponentToEdit: "Click a component on the canvas to edit its properties",
  propertySettings: "Property Settings",
  basicProperties: "Basic Properties",
  labelText: "Label Text",
  placeholder: "Placeholder",
  required: "Required",
  enterLabelText: "Enter label text",
  enterPlaceholderText: "Enter placeholder text",

  // Heading Settings
  headingSettings: "Heading Settings",
  headingLevel: "Heading Level",
  h1MainTitle: "H1 - Main Title",
  h2Subtitle: "H2 - Subtitle",
  h3ThirdLevel: "H3 - Third Level",
  h4FourthLevel: "H4 - Fourth Level",
  h5FifthLevel: "H5 - Fifth Level",
  h6SixthLevel: "H6 - Sixth Level",

  // Button Settings
  buttonSettings: "Button Settings",
  buttonStyle: "Button Style",
  primaryButton: "Primary Button",
  secondaryButton: "Secondary Button",
  outlineButton: "Outline Button",
  ghostButton: "Ghost Button",

  // Option Settings
  optionSettings: "Option Settings",
  addOption: "Add Option",
  optionLabel: "Option Label",

  // Validation Rules
  validationRules: "Validation Rules",
  minLength: "Min Length",
  maxLength: "Max Length",
  unlimited: "Unlimited",
  emailValidation: "Email Format Validation",
  phoneValidation: "Phone Format Validation",

  // Style Settings
  styleSettings: "Style Settings",
  width: "Width",
  alignment: "Alignment",
  leftAlign: "Left Align",
  centerAlign: "Center Align",
  rightAlign: "Right Align",

  // Design Canvas
  formTitleDefault: "Form Title",
  fillFollowingInfo: "Please fill in the following information",
  startDesigning: "Start designing your form",
  dragComponentsHere: "Drag components from the left panel to start designing",
  submit: "Submit",
  copyLabel: "Copy",


  // Form Preview
  formNotExistOrDeleted: "Form does not exist or has been deleted",
  formConfigNotExist: "Form configuration does not exist",
  loadingForm: "Loading form...",
  backToFormBuilder: "Back to Form Builder",
  backToList: "Back to List",
  previewMode: "Preview Mode",
  formId: "Form ID",
  editForm: "Edit form",
  copyForm: "Copy form",
  viewSubmissions: "View submissions",
  deleteForm: "Delete form",
  submitButtonText: "Submit Button Text",
  successMessage: "Success Message",
  errorMessage: "Error Message",
  allowMultipleSubmissions: "Allow Multiple Submissions",
  requireLogin: "Require Login",
  collectEmail: "Collect Email",
  yes: "Yes",
  no: "No",
  shareUrl: "Share URL",
  copyShareUrl: "Copy Share URL",
  shareUrlCopied: "Share URL copied to clipboard",
  downloadConfig: "Download Config",
  submitSuccess: "Submit Success",
  submitSuccessMessage: "Your form has been submitted successfully, thank you for your participation!",
  submitFailed: "Submit failed, please try again later",
  goHome: "Go Home",
  onlineForm: "Online Form",
  poweredBy: "Powered by",
  formBuilderName: "Form Builder",
  techSupport: "technical support",
  openInNewWindow:'Open in new window'
};










